import { TRPCError } from "@trpc/server";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { ZodError } from "zod";
import {
  AuthErrors,
  BusinessErrors,
  ERROR_SEVERITY,
  handleDatabaseError,
  handleValidationError,
  safeErrorHandler,
} from "@/lib/trpc/core/errors";

describe("Error Handling", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("safeErrorHandler", () => {
    it("should handle generic errors", () => {
      const error = new Error("Generic error");
      const result = safeErrorHandler(error, "test-operation");

      expect(result).toMatchObject({
        code: "INTERNAL_SERVER_ERROR",
        message: "Generic error",
        category: "system",
        severity: ERROR_SEVERITY.HIGH,
      });
    });

    it("should handle TRPC errors", () => {
      const error = new TRPCError({
        code: "BAD_REQUEST",
        message: "Invalid input",
      });

      const result = safeErrorHandler(error, "test-operation");

      expect(result).toMatchObject({
        code: "BAD_REQUEST",
        message: "Invalid input",
        category: "system",
        severity: ERROR_SEVERITY.MEDIUM,
      });
    });

    it("should include error details in development", () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      const error = new Error("Test error");
      error.stack = "Error stack trace";

      const result = safeErrorHandler(error, "test-operation");

      // The current implementation doesn't include details based on environment
      // This test documents the current behavior
      expect(result.timestamp).toBeDefined();
      expect(result.cause).toBe(error);

      process.env.NODE_ENV = originalEnv;
    });

    it("should not include sensitive details in production", () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "production";

      const error = new Error("Test error");
      error.stack = "Error stack trace";

      const result = safeErrorHandler(error, "test-operation");

      expect(result.details).toBeUndefined();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe("handleValidationError", () => {
    it("should handle Zod validation errors", () => {
      const zodError = new ZodError([
        {
          code: "invalid_type",
          expected: "string",
          received: "number",
          path: ["name"],
          message: "Expected string, received number",
        },
        {
          code: "too_small",
          minimum: 1,
          type: "string",
          inclusive: true,
          path: ["email"],
          message: "String must contain at least 1 character(s)",
        },
      ]);

      expect(() => handleValidationError(zodError)).toThrow(TRPCError);

      try {
        handleValidationError(zodError);
      } catch (error) {
        expect(error).toBeInstanceOf(TRPCError);
        expect((error as TRPCError).code).toBe("BAD_REQUEST");
        expect((error as TRPCError).message).toBe("Validation failed");
      }
    });

    it("should format validation error messages", () => {
      const zodError = new ZodError([
        {
          code: "invalid_type",
          expected: "string",
          received: "number",
          path: ["user", "name"],
          message: "Expected string, received number",
        },
      ]);

      expect(() => handleValidationError(zodError)).toThrow(TRPCError);

      try {
        handleValidationError(zodError);
      } catch (error) {
        expect(error).toBeInstanceOf(TRPCError);
        expect((error as TRPCError).message).toBe("Validation failed");
        // The error details would contain the formatted path information
      }
    });
  });

  describe("handleDatabaseError", () => {
    it("should handle unique constraint violations", () => {
      const dbError = new Error(
        "duplicate key value violates unique constraint",
      );

      expect(() => handleDatabaseError(dbError, "create-user")).toThrow(
        TRPCError,
      );

      try {
        handleDatabaseError(dbError, "create-user");
      } catch (error) {
        expect(error).toBeInstanceOf(TRPCError);
        expect((error as TRPCError).code).toBe("CONFLICT");
        expect((error as TRPCError).message).toContain("unique constraint");
      }
    });

    it("should handle foreign key constraint violations", () => {
      const dbError = new Error("violates foreign key constraint");

      expect(() => handleDatabaseError(dbError, "create-bid")).toThrow(
        TRPCError,
      );

      try {
        handleDatabaseError(dbError, "create-bid");
      } catch (error) {
        expect(error).toBeInstanceOf(TRPCError);
        expect((error as TRPCError).code).toBe("CONFLICT");
        expect((error as TRPCError).message).toContain(
          "foreign key constraint",
        );
      }
    });

    it("should handle connection errors", () => {
      const dbError = new Error("connection terminated");

      expect(() => handleDatabaseError(dbError, "get-jobs")).toThrow(TRPCError);

      try {
        handleDatabaseError(dbError, "get-jobs");
      } catch (error) {
        expect(error).toBeInstanceOf(TRPCError);
        expect((error as TRPCError).code).toBe("INTERNAL_SERVER_ERROR");
        expect((error as TRPCError).message).toContain("get-jobs failed");
      }
    });

    it("should handle timeout errors", () => {
      const dbError = new Error("query timeout");

      expect(() => handleDatabaseError(dbError, "complex-query")).toThrow(
        TRPCError,
      );

      try {
        handleDatabaseError(dbError, "complex-query");
      } catch (error) {
        expect(error).toBeInstanceOf(TRPCError);
        expect((error as TRPCError).code).toBe("INTERNAL_SERVER_ERROR");
        expect((error as TRPCError).message).toContain("complex-query failed");
      }
    });
  });

  describe("AuthErrors", () => {
    it("should create unauthorized error", () => {
      const error = AuthErrors.unauthorized();

      expect(error).toBeInstanceOf(TRPCError);
      expect(error.code).toBe("UNAUTHORIZED");
      expect(error.message).toBe("Authentication required");
    });

    it("should create forbidden error", () => {
      const error = AuthErrors.forbidden("Admin access required");

      expect(error).toBeInstanceOf(TRPCError);
      expect(error.code).toBe("FORBIDDEN");
      expect(error.message).toBe("Admin access required");
    });

    it("should create session expired error", () => {
      const error = AuthErrors.sessionExpired();

      expect(error).toBeInstanceOf(TRPCError);
      expect(error.code).toBe("SESSION_EXPIRED");
      expect(error.message).toBe("Session has expired");
    });

    it("should create insufficient permissions error", () => {
      const error = AuthErrors.insufficientPermissions("user", "delete");

      expect(error).toBeInstanceOf(TRPCError);
      expect(error.code).toBe("FORBIDDEN");
      expect(error.message).toContain("Insufficient permissions");
    });
  });

  describe("BusinessErrors", () => {
    it("should create job not published error", () => {
      const error = BusinessErrors.jobNotPublished();

      expect(error).toBeInstanceOf(TRPCError);
      expect(error.code).toBe("BAD_REQUEST");
      expect(error.message).toBe("Cannot bid on a job that is not published");
    });

    it("should create bid already exists error", () => {
      const error = BusinessErrors.bidAlreadyExists();

      expect(error).toBeInstanceOf(TRPCError);
      expect(error.code).toBe("CONFLICT");
      expect(error.message).toBe(
        "Your organization has already submitted a bid for this job",
      );
    });

    it("should create business rule violation error", () => {
      const error = BusinessErrors.ruleViolation("test rule");

      expect(error).toBeInstanceOf(TRPCError);
      expect(error.code).toBe("BAD_REQUEST");
      expect(error.message).toContain("Business rule violation: test rule");
    });

    it("should create operation not allowed error", () => {
      const error = BusinessErrors.operationNotAllowed(
        "delete",
        "resource is locked",
      );

      expect(error).toBeInstanceOf(TRPCError);
      expect(error.code).toBe("BAD_REQUEST");
      expect(error.message).toContain("Operation 'delete' is not allowed");
    });
  });

  describe("Error Severity", () => {
    it("should classify errors by severity", () => {
      expect(ERROR_SEVERITY.LOW).toBe("low");
      expect(ERROR_SEVERITY.MEDIUM).toBe("medium");
      expect(ERROR_SEVERITY.HIGH).toBe("high");
      expect(ERROR_SEVERITY.CRITICAL).toBe("critical");
    });
  });

  describe("Error Recovery", () => {
    it("should provide recovery suggestions", () => {
      const error = BusinessErrors.jobNotPublished();

      expect(error.message).toBe("Cannot bid on a job that is not published");
      // In a real implementation, you might include recovery suggestions
    });

    it("should handle rate limiting errors", () => {
      const rateLimitError = new Error("Too many requests");
      const result = safeErrorHandler(rateLimitError, "api-call");

      expect(result.code).toBe("INTERNAL_SERVER_ERROR");
      // In a real implementation, you might detect rate limiting and provide specific handling
    });
  });

  describe("Error Logging", () => {
    it("should include trace information", () => {
      const error = new Error("Test error");
      const result = safeErrorHandler(error, "test-operation");

      expect(result.timestamp).toBeDefined();
      expect(result.timestamp).toBeInstanceOf(Date);
    });

    it("should generate unique error IDs", () => {
      const error1 = safeErrorHandler(new Error("Error 1"), "op1");
      const error2 = safeErrorHandler(new Error("Error 2"), "op2");

      // In a real implementation, you might include unique error IDs
      expect(error1.timestamp).not.toBe(error2.timestamp);
    });
  });
});
