import { describe, it, expect, beforeEach, vi } from "vitest";
import { createId } from "@paralleldrive/cuid2";
import {
  createTestCaller,
  createAuthenticatedContext,
  createUnauthenticatedContext,
  resetDbMocks,
  mockDb,
  mockDbSuccess,
} from "@/test/utils/trpc-test-utils";
import { createTestUser, createTestOrganization, createTestMembership } from "@/test/fixtures";

// Mock external dependencies
vi.mock("@/lib/trpc/utils/permissions", () => ({
  getUserOrganization: vi.fn(),
  checkOrganizationMembership: vi.fn(),
}));

describe("Users Router", () => {
  beforeEach(() => {
    resetDbMocks();
    vi.clearAllMocks();
  });

  describe("me", () => {
    it("should return current user profile", async () => {
      const userId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const testUser = createTestUser({ id: userId, name: "<PERSON>" });

      mockDb.query.user.findFirst.mockResolvedValue(testUser);

      const result = await caller.users.me();

      expect(result).toEqual(testUser);
      expect(mockDb.query.user.findFirst).toHaveBeenCalledWith({
        where: expect.any(Object),
        with: expect.any(Object),
      });
    });

    it("should throw UNAUTHORIZED for unauthenticated user", async () => {
      const ctx = createUnauthenticatedContext();
      const caller = createTestCaller(ctx);

      await expect(caller.users.me()).rejects.toThrow(
        expect.objectContaining({
          code: "UNAUTHORIZED",
        })
      );
    });

    it("should throw NOT_FOUND when user doesn't exist", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      mockDb.query.user.findFirst.mockResolvedValue(null);

      await expect(caller.users.me()).rejects.toThrow(
        expect.objectContaining({
          code: "NOT_FOUND",
          message: "User not found",
        })
      );
    });
  });

  describe("updateProfile", () => {
    it("should update user profile successfully", async () => {
      const userId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const updateData = {
        name: "Updated Name",
        email: "<EMAIL>",
      };

      const existingUser = createTestUser({ id: userId });
      const updatedUser = { ...existingUser, ...updateData };

      mockDb.query.user.findFirst.mockResolvedValue(existingUser);
      mockDbSuccess();
      mockDb.update().set().where().returning.mockResolvedValue([updatedUser]);

      const result = await caller.users.updateProfile(updateData);

      expect(result).toEqual([updatedUser]);
      expect(mockDb.update).toHaveBeenCalled();
    });

    it("should validate email format", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      const invalidData = {
        name: "Valid Name",
        email: "invalid-email",
      };

      await expect(caller.users.updateProfile(invalidData)).rejects.toThrow();
    });

    it("should handle duplicate email error", async () => {
      const userId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const updateData = {
        name: "Updated Name",
        email: "<EMAIL>",
      };

      const existingUser = createTestUser({ id: userId });
      mockDb.query.user.findFirst.mockResolvedValue(existingUser);

      const duplicateError = new Error("duplicate key value violates unique constraint");
      mockDb.update().set().where().returning.mockRejectedValue(duplicateError);

      await expect(caller.users.updateProfile(updateData)).rejects.toThrow();
    });
  });

  describe("updateSettings", () => {
    it("should update user settings successfully", async () => {
      const userId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const settingsData = {
        notifications: {
          email: true,
          push: false,
          sms: true,
        },
        preferences: {
          theme: "dark",
          language: "en",
        },
      };

      const existingUser = createTestUser({ id: userId });
      const updatedUser = { ...existingUser, settings: settingsData };

      mockDb.query.user.findFirst.mockResolvedValue(existingUser);
      mockDbSuccess();
      mockDb.update().set().where().returning.mockResolvedValue([updatedUser]);

      const result = await caller.users.updateSettings(settingsData);

      expect(result).toEqual([updatedUser]);
      expect(mockDb.update).toHaveBeenCalledWith(expect.any(Object));
    });

    it("should validate settings structure", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      const invalidSettings = {
        notifications: "invalid", // Should be object
      };

      await expect(caller.users.updateSettings(invalidSettings as any)).rejects.toThrow();
    });
  });

  describe("getOrganization", () => {
    it("should return user's organization", async () => {
      const userId = createId();
      const organizationId = createId();
      const ctx = createAuthenticatedContext(userId, "contractor", organizationId);
      const caller = createTestCaller(ctx);

      const testOrganization = createTestOrganization({ id: organizationId });

      const { getUserOrganization } = await import("@/lib/trpc/utils/permissions");
      vi.mocked(getUserOrganization).mockResolvedValue(testOrganization);

      const result = await caller.users.getOrganization();

      expect(result).toEqual(testOrganization);
      expect(getUserOrganization).toHaveBeenCalledWith(userId);
    });

    it("should return null when user has no organization", async () => {
      const userId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const { getUserOrganization } = await import("@/lib/trpc/utils/permissions");
      vi.mocked(getUserOrganization).mockResolvedValue(null);

      const result = await caller.users.getOrganization();

      expect(result).toBeNull();
    });
  });

  describe("getPublicProfile", () => {
    it("should return public user profile", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      const targetUserId = createId();
      const testUser = createTestUser({ 
        id: targetUserId, 
        name: "Public User",
        email: "<EMAIL>" 
      });

      mockDb.query.user.findFirst.mockResolvedValue(testUser);

      const result = await caller.users.getPublicProfile({ id: targetUserId });

      // Should return user without sensitive information
      expect(result).toEqual(
        expect.objectContaining({
          id: targetUserId,
          name: "Public User",
          // Should not include email or other sensitive data
        })
      );
      expect(result).not.toHaveProperty("email");
    });

    it("should throw NOT_FOUND for non-existent user", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      mockDb.query.user.findFirst.mockResolvedValue(null);

      await expect(caller.users.getPublicProfile({ id: createId() })).rejects.toThrow(
        expect.objectContaining({
          code: "NOT_FOUND",
        })
      );
    });
  });

  describe("searchUsers", () => {
    it("should search users by name or email", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      const searchQuery = "john";
      const testUsers = [
        createTestUser({ name: "John Doe", email: "<EMAIL>" }),
        createTestUser({ name: "Johnny Smith", email: "<EMAIL>" }),
      ];

      mockDb.query.user.findMany.mockResolvedValue(testUsers);

      const result = await caller.users.searchUsers({ query: searchQuery });

      expect(result).toEqual(testUsers);
      expect(mockDb.query.user.findMany).toHaveBeenCalledWith({
        where: expect.any(Object),
        limit: expect.any(Number),
      });
    });

    it("should limit search results", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      const searchQuery = "test";
      const limit = 5;

      mockDb.query.user.findMany.mockResolvedValue([]);

      await caller.users.searchUsers({ query: searchQuery, limit });

      expect(mockDb.query.user.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          limit,
        })
      );
    });

    it("should validate search query length", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      await expect(caller.users.searchUsers({ query: "a" })).rejects.toThrow();
      await expect(caller.users.searchUsers({ query: "" })).rejects.toThrow();
    });
  });

  describe("error handling", () => {
    it("should handle database errors gracefully", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      const dbError = new Error("Database connection failed");
      mockDb.query.user.findFirst.mockRejectedValue(dbError);

      await expect(caller.users.me()).rejects.toThrow();
    });
  });
});
