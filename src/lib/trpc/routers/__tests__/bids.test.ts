import { describe, it, expect, beforeEach, vi } from "vitest";
import { TRPCError } from "@trpc/server";
import { createId } from "@paralleldrive/cuid2";
import {
  createTestCaller,
  createAuthenticatedContext,
  createUnauthenticatedContext,
  resetDbMocks,
  mockDb,
  mockDbSuccess,
} from "@/test/utils/trpc-test-utils";
import {
  createTestBid,
  createTestJob,
  createTestOrganization,
  createTestUser,
  createTestMembership,
} from "@/test/fixtures";

// Mock external dependencies
vi.mock("@/lib/email/send-bid-notification", () => ({
  sendBidNotificationEmail: vi.fn(),
}));

vi.mock("@/lib/trpc/utils/permissions", () => ({
  checkJobOwnership: vi.fn(),
  checkOrganizationMembership: vi.fn(),
  getUserOrganization: vi.fn(),
  requireAuth: vi.fn(),
}));

vi.mock("@/lib/trpc/utils/entities", () => ({
  getBidWithRelations: vi.fn(),
  getJobWithRelations: vi.fn(),
}));

describe("Bids Router", () => {
  beforeEach(() => {
    resetDbMocks();
    vi.clearAllMocks();
  });

  describe("listForOrganization", () => {
    it("should return empty array when user has no organization", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      const { getUserOrganization } = await import("@/lib/trpc/utils/permissions");
      vi.mocked(getUserOrganization).mockResolvedValue(null);

      const result = await caller.bids.listForOrganization();

      expect(result).toEqual([]);
      expect(getUserOrganization).toHaveBeenCalledWith(ctx.userId);
    });

    it("should return bids for user's organization", async () => {
      const userId = createId();
      const organizationId = createId();
      const ctx = createAuthenticatedContext(userId, "contractor", organizationId);
      const caller = createTestCaller(ctx);

      const testOrganization = createTestOrganization({ id: organizationId });
      const testBids = [
        createTestBid({ organizationId }),
        createTestBid({ organizationId }),
      ];

      const { getUserOrganization } = await import("@/lib/trpc/utils/permissions");
      vi.mocked(getUserOrganization).mockResolvedValue(testOrganization);
      mockDb.query.bid.findMany.mockResolvedValue(testBids);

      const result = await caller.bids.listForOrganization();

      expect(result).toEqual(testBids);
      expect(mockDb.query.bid.findMany).toHaveBeenCalledWith({
        where: expect.any(Object),
        with: expect.any(Object),
        orderBy: expect.any(Array),
        extras: expect.any(Object),
      });
    });
  });

  describe("create", () => {
    it("should throw UNAUTHORIZED when user is not authenticated", async () => {
      const ctx = createUnauthenticatedContext();
      const caller = createTestCaller(ctx);

      const bidData = {
        jobId: createId(),
        organizationId: createId(),
        name: "Test Bid",
        amount: 1000,
        description: "Test bid description",
        estimatedDuration: 7,
      };

      await expect(caller.bids.create(bidData)).rejects.toThrow(
        expect.objectContaining({
          code: "UNAUTHORIZED",
        })
      );
    });

    it("should create a bid successfully", async () => {
      const userId = createId();
      const organizationId = createId();
      const jobId = createId();
      const ctx = createAuthenticatedContext(userId, "contractor", organizationId);
      const caller = createTestCaller(ctx);

      const bidData = {
        jobId,
        organizationId,
        name: "Test Bid",
        amount: 1000,
        description: "Test bid description",
        estimatedDuration: 7,
      };

      const testJob = createTestJob({ id: jobId, status: "PUBLISHED" });
      const createdBid = createTestBid({ ...bidData, id: createId() });

      const { getJobWithRelations } = await import("@/lib/trpc/utils/entities");
      const { checkOrganizationMembership } = await import("@/lib/trpc/utils/permissions");
      
      vi.mocked(getJobWithRelations).mockResolvedValue(testJob as any);
      vi.mocked(checkOrganizationMembership).mockResolvedValue(true);
      
      mockDbSuccess();
      mockDb.insert().values().returning.mockResolvedValue([createdBid]);

      const result = await caller.bids.create(bidData);

      expect(result).toEqual([createdBid]);
      expect(getJobWithRelations).toHaveBeenCalledWith(jobId);
      expect(checkOrganizationMembership).toHaveBeenCalledWith(userId, organizationId);
    });

    it("should throw error when job is not published", async () => {
      const userId = createId();
      const organizationId = createId();
      const jobId = createId();
      const ctx = createAuthenticatedContext(userId, "contractor", organizationId);
      const caller = createTestCaller(ctx);

      const bidData = {
        jobId,
        organizationId,
        name: "Test Bid",
        amount: 1000,
        description: "Test bid description",
        estimatedDuration: 7,
      };

      const testJob = createTestJob({ id: jobId, status: "DRAFT" });

      const { getJobWithRelations } = await import("@/lib/trpc/utils/entities");
      const { checkOrganizationMembership } = await import("@/lib/trpc/utils/permissions");
      
      vi.mocked(getJobWithRelations).mockResolvedValue(testJob as any);
      vi.mocked(checkOrganizationMembership).mockResolvedValue(true);

      await expect(caller.bids.create(bidData)).rejects.toThrow(
        expect.objectContaining({
          code: "BAD_REQUEST",
          message: "Job is not published",
        })
      );
    });
  });

  describe("getById", () => {
    it("should throw UNAUTHORIZED when user is not authenticated", async () => {
      const ctx = createUnauthenticatedContext();
      const caller = createTestCaller(ctx);

      await expect(caller.bids.getById({ id: createId() })).rejects.toThrow(
        expect.objectContaining({
          code: "UNAUTHORIZED",
        })
      );
    });

    it("should return bid when user has permission", async () => {
      const userId = createId();
      const bidId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const testBid = createTestBid({ id: bidId });

      const { getBidWithRelations } = await import("@/lib/trpc/utils/entities");
      const { checkJobOwnership, checkOrganizationMembership, requireAuth } = await import("@/lib/trpc/utils/permissions");
      
      vi.mocked(getBidWithRelations).mockResolvedValue(testBid as any);
      vi.mocked(checkJobOwnership).mockResolvedValue(true);
      vi.mocked(checkOrganizationMembership).mockResolvedValue(false);
      vi.mocked(requireAuth).mockImplementation((condition, message) => {
        if (!condition) throw new TRPCError({ code: "FORBIDDEN", message });
      });

      const result = await caller.bids.getById({ id: bidId });

      expect(result).toEqual(testBid);
      expect(getBidWithRelations).toHaveBeenCalledWith(bidId);
    });
  });

  describe("accept", () => {
    it("should accept a bid successfully", async () => {
      const userId = createId();
      const bidId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const testBid = createTestBid({ id: bidId, status: "PROPOSED" });
      const updatedBid = { ...testBid, status: "ACCEPTED" as const };

      const { getBidWithRelations } = await import("@/lib/trpc/utils/entities");
      const { checkJobOwnership, requireAuth } = await import("@/lib/trpc/utils/permissions");
      
      vi.mocked(getBidWithRelations).mockResolvedValue(testBid as any);
      vi.mocked(checkJobOwnership).mockResolvedValue(true);
      vi.mocked(requireAuth).mockImplementation(() => {});
      
      mockDbSuccess();
      mockDb.update().set().where().returning.mockResolvedValue([updatedBid]);

      const result = await caller.bids.accept({ id: bidId });

      expect(result).toEqual([updatedBid]);
    });
  });

  describe("withdraw", () => {
    it("should withdraw a bid successfully", async () => {
      const userId = createId();
      const organizationId = createId();
      const bidId = createId();
      const ctx = createAuthenticatedContext(userId, "contractor", organizationId);
      const caller = createTestCaller(ctx);

      const testBid = createTestBid({ id: bidId, organizationId, status: "PROPOSED" });
      const updatedBid = { ...testBid, status: "WITHDRAWN" as const };

      const { getBidWithRelations } = await import("@/lib/trpc/utils/entities");
      const { checkOrganizationMembership, requireAuth } = await import("@/lib/trpc/utils/permissions");
      
      vi.mocked(getBidWithRelations).mockResolvedValue(testBid as any);
      vi.mocked(checkOrganizationMembership).mockResolvedValue(true);
      vi.mocked(requireAuth).mockImplementation(() => {});
      
      mockDbSuccess();
      mockDb.update().set().where().returning.mockResolvedValue([updatedBid]);

      const result = await caller.bids.withdraw({ id: bidId });

      expect(result).toEqual([updatedBid]);
    });
  });
});
