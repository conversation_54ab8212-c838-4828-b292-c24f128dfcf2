import { describe, it, expect, beforeEach, vi } from "vitest";
import { createId } from "@paralleldrive/cuid2";
import {
  createTestCaller,
  createAuthenticatedContext,
  createUnauthenticatedContext,
  resetDbMocks,
  mockDb,
  mockDbSuccess,
} from "@/test/utils/trpc-test-utils";
import { createTestProperty, createTestAddress } from "@/test/fixtures";

// Mock external dependencies
vi.mock("@/lib/geocoding", () => ({
  geocodeAddress: vi.fn().mockResolvedValue({
    latitude: 40.7128,
    longitude: -74.0060,
  }),
}));

vi.mock("@/lib/trpc/utils/addresses", () => ({
  createAddress: vi.fn(),
}));

describe("Properties Router", () => {
  beforeEach(() => {
    resetDbMocks();
    vi.clearAllMocks();
  });

  describe("list", () => {
    it("should return user's properties", async () => {
      const userId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const testProperties = [
        createTestProperty({ userId, name: "Property 1" }),
        createTestProperty({ userId, name: "Property 2" }),
      ];

      mockDb.query.property.findMany.mockResolvedValue(testProperties);

      const result = await caller.properties.list();

      expect(result).toEqual(testProperties);
      expect(mockDb.query.property.findMany).toHaveBeenCalledWith({
        where: expect.any(Object),
        with: {
          address: true,
        },
        orderBy: expect.any(Array),
      });
    });

    it("should throw UNAUTHORIZED for unauthenticated user", async () => {
      const ctx = createUnauthenticatedContext();
      const caller = createTestCaller(ctx);

      await expect(caller.properties.list()).rejects.toThrow(
        expect.objectContaining({
          code: "UNAUTHORIZED",
        })
      );
    });
  });

  describe("create", () => {
    it("should create a property successfully", async () => {
      const userId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const propertyData = {
        name: "New Property",
        imageUrl: "https://example.com/image.jpg",
        address: {
          street: "123 Main St",
          city: "Test City",
          state: "TS",
          zip: "12345",
        },
      };

      const createdAddress = createTestAddress(propertyData.address);
      const createdProperty = createTestProperty({
        name: propertyData.name,
        imageUrl: propertyData.imageUrl,
        addressId: createdAddress.id,
        userId,
      });

      const { createAddress } = await import("@/lib/trpc/utils/addresses");
      vi.mocked(createAddress).mockResolvedValue(createdAddress);

      mockDbSuccess();
      mockDb.insert().values().returning.mockResolvedValue([createdProperty]);

      const result = await caller.properties.create(propertyData);

      expect(result).toEqual([createdProperty]);
      expect(createAddress).toHaveBeenCalledWith(propertyData.address);
    });

    it("should validate required fields", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      const invalidPropertyData = {
        name: "", // Empty name should fail
        imageUrl: "invalid-url", // Invalid URL should fail
        address: {
          street: "",
          city: "",
          state: "",
          zip: "",
        },
      };

      await expect(caller.properties.create(invalidPropertyData)).rejects.toThrow();
    });

    it("should handle geocoding errors gracefully", async () => {
      const userId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const propertyData = {
        name: "New Property",
        imageUrl: "https://example.com/image.jpg",
        address: {
          street: "Invalid Address",
          city: "Nowhere",
          state: "XX",
          zip: "00000",
        },
      };

      const { geocodeAddress } = await import("@/lib/geocoding");
      vi.mocked(geocodeAddress).mockRejectedValue(new Error("Geocoding failed"));

      const { createAddress } = await import("@/lib/trpc/utils/addresses");
      vi.mocked(createAddress).mockResolvedValue(createTestAddress());

      mockDbSuccess();
      mockDb.insert().values().returning.mockResolvedValue([createTestProperty()]);

      // Should still create property even if geocoding fails
      const result = await caller.properties.create(propertyData);

      expect(result).toBeDefined();
    });
  });

  describe("one", () => {
    it("should return a property by ID for owner", async () => {
      const userId = createId();
      const propertyId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const testProperty = createTestProperty({ id: propertyId, userId });

      mockDb.query.property.findFirst.mockResolvedValue(testProperty);

      const result = await caller.properties.one({ id: propertyId });

      expect(result).toEqual(testProperty);
      expect(mockDb.query.property.findFirst).toHaveBeenCalledWith({
        where: expect.any(Object),
        with: {
          address: true,
        },
      });
    });

    it("should throw FORBIDDEN for non-owner", async () => {
      const userId = createId();
      const otherUserId = createId();
      const propertyId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const testProperty = createTestProperty({ id: propertyId, userId: otherUserId });

      mockDb.query.property.findFirst.mockResolvedValue(testProperty);

      await expect(caller.properties.one({ id: propertyId })).rejects.toThrow(
        expect.objectContaining({
          code: "FORBIDDEN",
        })
      );
    });

    it("should throw NOT_FOUND for non-existent property", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      mockDb.query.property.findFirst.mockResolvedValue(null);

      await expect(caller.properties.one({ id: createId() })).rejects.toThrow(
        expect.objectContaining({
          code: "NOT_FOUND",
        })
      );
    });
  });

  describe("update", () => {
    it("should update a property successfully", async () => {
      const userId = createId();
      const propertyId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const updateData = {
        id: propertyId,
        name: "Updated Property Name",
        imageUrl: "https://example.com/new-image.jpg",
      };

      const existingProperty = createTestProperty({ id: propertyId, userId });
      const updatedProperty = { ...existingProperty, ...updateData };

      mockDb.query.property.findFirst.mockResolvedValue(existingProperty);
      mockDbSuccess();
      mockDb.update().set().where().returning.mockResolvedValue([updatedProperty]);

      const result = await caller.properties.update(updateData);

      expect(result).toEqual([updatedProperty]);
    });

    it("should throw FORBIDDEN for non-owner", async () => {
      const userId = createId();
      const otherUserId = createId();
      const propertyId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const updateData = {
        id: propertyId,
        name: "Updated Property Name",
      };

      const existingProperty = createTestProperty({ id: propertyId, userId: otherUserId });

      mockDb.query.property.findFirst.mockResolvedValue(existingProperty);

      await expect(caller.properties.update(updateData)).rejects.toThrow(
        expect.objectContaining({
          code: "FORBIDDEN",
        })
      );
    });
  });

  describe("delete", () => {
    it("should delete a property successfully", async () => {
      const userId = createId();
      const propertyId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const existingProperty = createTestProperty({ id: propertyId, userId });

      mockDb.query.property.findFirst.mockResolvedValue(existingProperty);
      mockDbSuccess();
      mockDb.delete().where().returning.mockResolvedValue([existingProperty]);

      const result = await caller.properties.delete({ id: propertyId });

      expect(result).toEqual([existingProperty]);
    });

    it("should throw FORBIDDEN for non-owner", async () => {
      const userId = createId();
      const otherUserId = createId();
      const propertyId = createId();
      const ctx = createAuthenticatedContext(userId);
      const caller = createTestCaller(ctx);

      const existingProperty = createTestProperty({ id: propertyId, userId: otherUserId });

      mockDb.query.property.findFirst.mockResolvedValue(existingProperty);

      await expect(caller.properties.delete({ id: propertyId })).rejects.toThrow(
        expect.objectContaining({
          code: "FORBIDDEN",
        })
      );
    });
  });
});
