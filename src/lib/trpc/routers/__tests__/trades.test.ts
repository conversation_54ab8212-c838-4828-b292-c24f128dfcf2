import { describe, it, expect, beforeEach, vi } from "vitest";
import { createId } from "@paralleldrive/cuid2";
import {
  createTestCaller,
  createAuthenticatedContext,
  createUnauthenticatedContext,
  resetDbMocks,
  mockDb,
  mockDbSuccess,
} from "@/test/utils/trpc-test-utils";
import { createTestTrade } from "@/test/fixtures";

describe("Trades Router", () => {
  beforeEach(() => {
    resetDbMocks();
    vi.clearAllMocks();
  });

  describe("list", () => {
    it("should return list of trades for authenticated user", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      const testTrades = [
        createTestTrade({ name: "Plumbing" }),
        createTestTrade({ name: "Electrical" }),
        createTestTrade({ name: "Carpentry" }),
      ];

      mockDb.query.trade.findMany.mockResolvedValue(testTrades);
      mockDb.$count.mockReturnValue("organizationCount");

      const result = await caller.trades.list();

      expect(result).toEqual(testTrades);
      expect(mockDb.query.trade.findMany).toHaveBeenCalledWith({
        orderBy: expect.any(Array),
        with: {
          organizations: true,
        },
        extras: {
          organizationCount: "organizationCount",
        },
      });
    });

    it("should throw UNAUTHORIZED for unauthenticated user", async () => {
      const ctx = createUnauthenticatedContext();
      const caller = createTestCaller(ctx);

      await expect(caller.trades.list()).rejects.toThrow(
        expect.objectContaining({
          code: "UNAUTHORIZED",
        })
      );
    });
  });

  describe("create", () => {
    it("should create a trade successfully for admin user", async () => {
      const ctx = createAuthenticatedContext(createId(), "admin");
      const caller = createTestCaller(ctx);

      const tradeData = {
        name: "New Trade",
        availableForQuickHire: true,
      };

      const createdTrade = createTestTrade(tradeData);

      mockDbSuccess();
      mockDb.insert().values().returning.mockResolvedValue([createdTrade]);

      const result = await caller.trades.create(tradeData);

      expect(result).toEqual([createdTrade]);
      expect(mockDb.insert).toHaveBeenCalled();
    });

    it("should throw FORBIDDEN for non-admin user", async () => {
      const ctx = createAuthenticatedContext(createId(), "homeowner");
      const caller = createTestCaller(ctx);

      const tradeData = {
        name: "New Trade",
        availableForQuickHire: true,
      };

      await expect(caller.trades.create(tradeData)).rejects.toThrow(
        expect.objectContaining({
          code: "FORBIDDEN",
        })
      );
    });

    it("should validate required fields", async () => {
      const ctx = createAuthenticatedContext(createId(), "admin");
      const caller = createTestCaller(ctx);

      const invalidTradeData = {
        name: "", // Empty name should fail validation
        availableForQuickHire: true,
      };

      await expect(caller.trades.create(invalidTradeData)).rejects.toThrow();
    });
  });

  describe("one", () => {
    it("should return a single trade by ID", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      const tradeId = createId();
      const testTrade = createTestTrade({ id: tradeId, name: "Plumbing" });

      mockDb.query.trade.findFirst.mockResolvedValue(testTrade);

      const result = await caller.trades.one({ id: tradeId });

      expect(result).toEqual(testTrade);
      expect(mockDb.query.trade.findFirst).toHaveBeenCalledWith({
        where: expect.any(Object),
      });
    });

    it("should return null for non-existent trade", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      const tradeId = createId();

      mockDb.query.trade.findFirst.mockResolvedValue(null);

      const result = await caller.trades.one({ id: tradeId });

      expect(result).toBeNull();
    });

    it("should validate ID format", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      await expect(caller.trades.one({ id: "" })).rejects.toThrow();
      await expect(caller.trades.one({ id: "invalid-id" })).rejects.toThrow();
    });
  });

  describe("update", () => {
    it("should update a trade successfully for admin user", async () => {
      const ctx = createAuthenticatedContext(createId(), "admin");
      const caller = createTestCaller(ctx);

      const tradeId = createId();
      const updateData = {
        id: tradeId,
        name: "Updated Trade Name",
        availableForQuickHire: false,
      };

      const updatedTrade = createTestTrade(updateData);

      mockDbSuccess();
      mockDb.update().set().where().returning.mockResolvedValue([updatedTrade]);

      const result = await caller.trades.update(updateData);

      expect(result).toEqual([updatedTrade]);
      expect(mockDb.update).toHaveBeenCalled();
    });

    it("should throw FORBIDDEN for non-admin user", async () => {
      const ctx = createAuthenticatedContext(createId(), "contractor");
      const caller = createTestCaller(ctx);

      const updateData = {
        id: createId(),
        name: "Updated Trade Name",
        availableForQuickHire: false,
      };

      await expect(caller.trades.update(updateData)).rejects.toThrow(
        expect.objectContaining({
          code: "FORBIDDEN",
        })
      );
    });
  });

  describe("delete", () => {
    it("should delete a trade successfully for admin user", async () => {
      const ctx = createAuthenticatedContext(createId(), "admin");
      const caller = createTestCaller(ctx);

      const tradeId = createId();
      const deletedTrade = createTestTrade({ id: tradeId });

      mockDbSuccess();
      mockDb.delete().where().returning.mockResolvedValue([deletedTrade]);

      const result = await caller.trades.delete({ id: tradeId });

      expect(result).toEqual([deletedTrade]);
      expect(mockDb.delete).toHaveBeenCalled();
    });

    it("should throw FORBIDDEN for non-admin user", async () => {
      const ctx = createAuthenticatedContext(createId(), "homeowner");
      const caller = createTestCaller(ctx);

      await expect(caller.trades.delete({ id: createId() })).rejects.toThrow(
        expect.objectContaining({
          code: "FORBIDDEN",
        })
      );
    });
  });

  describe("error handling", () => {
    it("should handle database errors gracefully", async () => {
      const ctx = createAuthenticatedContext();
      const caller = createTestCaller(ctx);

      const dbError = new Error("Database connection failed");
      mockDb.query.trade.findMany.mockRejectedValue(dbError);

      await expect(caller.trades.list()).rejects.toThrow();
    });

    it("should handle validation errors", async () => {
      const ctx = createAuthenticatedContext(createId(), "admin");
      const caller = createTestCaller(ctx);

      // Test with invalid data types
      await expect(
        caller.trades.create({
          name: 123 as any, // Should be string
          availableForQuickHire: "yes" as any, // Should be boolean
        })
      ).rejects.toThrow();
    });
  });
});
