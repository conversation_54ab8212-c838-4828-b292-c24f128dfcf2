import { createId } from "@paralleldrive/cuid2";
import type {
  User,
  Organization,
  Job,
  Bid,
  Property,
  Address,
  Trade,
  Membership,
  Message,
  Chat,
  Review,
  Schedule,
  JobTemplate,
} from "@/db/schema";

/**
 * Create a test user
 */
export function createTestUser(overrides: Partial<User> = {}): User {
  return {
    id: createId(),
    name: "Test User",
    email: "<EMAIL>",
    emailVerified: true,
    image: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    role: "homeowner",
    banned: false,
    banReason: null,
    banExpires: null,
    twoFactorEnabled: false,
    onboardingComplete: true,
    settings: {},
    ...overrides,
  };
}

/**
 * Create a test address
 */
export function createTestAddress(overrides: Partial<Address> = {}): Address {
  return {
    id: createId(),
    street: "123 Test St",
    city: "Test City",
    state: "TS",
    zip: "12345",
    createdAt: new Date(),
    updatedAt: new Date(),
    location: null,
    ...overrides,
  };
}

/**
 * Create a test property
 */
export function createTestProperty(overrides: Partial<Property> = {}): Property {
  return {
    id: createId(),
    name: "Test Property",
    imageUrl: "https://example.com/image.jpg",
    addressId: createId(),
    userId: createId(),
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
}

/**
 * Create a test trade
 */
export function createTestTrade(overrides: Partial<Trade> = {}): Trade {
  return {
    id: createId(),
    name: "Test Trade",
    createdAt: new Date(),
    updatedAt: new Date(),
    availableForQuickHire: false,
    ...overrides,
  };
}

/**
 * Create a test organization
 */
export function createTestOrganization(overrides: Partial<Organization> = {}): Organization {
  return {
    id: createId(),
    name: "Test Organization",
    tradeId: createId(),
    createdAt: new Date(),
    updatedAt: new Date(),
    description: "Test organization description",
    logoUrl: null,
    email: "<EMAIL>",
    phone: "555-0123",
    addressId: createId(),
    acceptsQuickHire: false,
    ...overrides,
  };
}

/**
 * Create a test job
 */
export function createTestJob(overrides: Partial<Job> = {}): Job {
  const now = new Date();
  const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
  const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

  return {
    id: createId(),
    name: "Test Job",
    propertyId: createId(),
    budget: 1000,
    taskBids: false,
    startsAt: tomorrow,
    deadline: nextWeek,
    createdAt: now,
    updatedAt: now,
    status: "DRAFT",
    completedAt: null,
    contractorCompleted: false,
    homeownerCompleted: false,
    isRecurring: false,
    jobType: "STANDARD",
    recurringFrequency: null,
    ...overrides,
  };
}

/**
 * Create a test bid
 */
export function createTestBid(overrides: Partial<Bid> = {}): Bid {
  return {
    id: createId(),
    name: "Test Bid",
    jobId: createId(),
    organizationId: createId(),
    createdAt: new Date(),
    updatedAt: new Date(),
    status: "PROPOSED",
    amount: 500,
    description: "Test bid description",
    estimatedDuration: 7,
    ...overrides,
  };
}

/**
 * Create a test membership
 */
export function createTestMembership(overrides: Partial<Membership> = {}): Membership {
  return {
    id: createId(),
    userId: createId(),
    organizationId: createId(),
    role: "MEMBER",
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
}

/**
 * Create a test chat
 */
export function createTestChat(overrides: Partial<Chat> = {}): Chat {
  return {
    id: createId(),
    jobId: createId(),
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
}

/**
 * Create a test message
 */
export function createTestMessage(overrides: Partial<Message> = {}): Message {
  return {
    id: createId(),
    content: "Test message",
    senderId: createId(),
    senderType: "user",
    createdAt: new Date(),
    updatedAt: new Date(),
    chatId: createId(),
    senderAvatarUrl: null,
    senderInitials: "TU",
    commandData: null,
    isCommand: false,
    ...overrides,
  };
}

/**
 * Create a test review
 */
export function createTestReview(overrides: Partial<Review> = {}): Review {
  return {
    id: createId(),
    jobId: createId(),
    rating: 5,
    comment: "Great work!",
    reviewType: "HOMEOWNER_REVIEW",
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
}

/**
 * Create a test schedule
 */
export function createTestSchedule(overrides: Partial<Schedule> = {}): Schedule {
  const now = new Date();
  const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

  return {
    id: createId(),
    jobId: createId(),
    organizationId: createId(),
    scheduledAt: tomorrow,
    duration: 480, // 8 hours in minutes
    notes: "Test schedule notes",
    createdAt: now,
    updatedAt: now,
    ...overrides,
  };
}

/**
 * Create a test job template
 */
export function createTestJobTemplate(overrides: Partial<JobTemplate> = {}): JobTemplate {
  return {
    id: createId(),
    name: "Test Template",
    description: "Test template description",
    budget: 1000,
    estimatedDuration: 7,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
}
