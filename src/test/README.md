# Testing Guide

This directory contains comprehensive tests for the tRPC routers and related functionality.

## Structure

```
src/test/
├── README.md                 # This file
├── setup.ts                  # Test setup and configuration
├── utils/
│   └── trpc-test-utils.ts   # Utilities for testing tRPC routers
├── fixtures/
│   └── index.ts             # Test data fixtures
├── mocks/
│   ├── server.ts            # MSW server setup
│   └── handlers.ts          # MSW request handlers
└── lib/trpc/routers/__tests__/
    ├── template.test.ts     # Template for new router tests
    ├── bids.test.ts        # Bids router tests
    ├── trades.test.ts      # Trades router tests
    ├── properties.test.ts  # Properties router tests
    ├── users.test.ts       # Users router tests
    └── ...                 # Other router tests
```

## Running Tests

### All Tests
```bash
pnpm test
```

### Watch Mode
```bash
pnpm test:watch
```

### With UI
```bash
pnpm test:ui
```

### Coverage Report
```bash
pnpm test:coverage
```

### Router Tests Only
```bash
pnpm test:routers
```

### Specific Router
```bash
pnpm test src/lib/trpc/routers/__tests__/bids.test.ts
```

## Writing Tests

### 1. Use the Template

Copy `template.test.ts` and modify it for your router:

```bash
cp src/lib/trpc/routers/__tests__/template.test.ts src/lib/trpc/routers/__tests__/your-router.test.ts
```

### 2. Test Structure

Each router test should include:

- **Authentication tests** - Verify proper auth handling
- **Authorization tests** - Check permission enforcement
- **Validation tests** - Test input validation
- **Business logic tests** - Verify router-specific logic
- **Error handling tests** - Test error scenarios
- **Database interaction tests** - Mock and verify DB calls

### 3. Test Utilities

#### Creating Test Contexts

```typescript
import { 
  createAuthenticatedContext, 
  createUnauthenticatedContext,
  createTestCaller 
} from "@/test/utils/trpc-test-utils";

// Authenticated user
const ctx = createAuthenticatedContext(userId, "homeowner");
const caller = createTestCaller(ctx);

// Admin user
const adminCtx = createAuthenticatedContext(userId, "admin");
const adminCaller = createTestCaller(adminCtx);

// Unauthenticated
const unauthCtx = createUnauthenticatedContext();
const unauthCaller = createTestCaller(unauthCtx);
```

#### Using Test Fixtures

```typescript
import { 
  createTestUser, 
  createTestJob, 
  createTestBid 
} from "@/test/fixtures";

const testUser = createTestUser({ name: "John Doe" });
const testJob = createTestJob({ budget: 1000 });
```

#### Mocking Database

```typescript
import { mockDb, resetDbMocks, mockDbSuccess } from "@/test/utils/trpc-test-utils";

beforeEach(() => {
  resetDbMocks();
});

// Mock successful operations
mockDbSuccess();

// Mock specific queries
mockDb.query.user.findFirst.mockResolvedValue(testUser);
mockDb.insert().values().returning.mockResolvedValue([createdItem]);
```

### 4. Common Test Patterns

#### Testing Authentication

```typescript
it("should throw UNAUTHORIZED for unauthenticated user", async () => {
  const ctx = createUnauthenticatedContext();
  const caller = createTestCaller(ctx);

  await expect(caller.router.procedure()).rejects.toThrow(
    expect.objectContaining({
      code: "UNAUTHORIZED",
    })
  );
});
```

#### Testing Authorization

```typescript
it("should throw FORBIDDEN for insufficient permissions", async () => {
  const ctx = createAuthenticatedContext(createId(), "homeowner");
  const caller = createTestCaller(ctx);

  await expect(caller.router.adminOnlyProcedure()).rejects.toThrow(
    expect.objectContaining({
      code: "FORBIDDEN",
    })
  );
});
```

#### Testing Validation

```typescript
it("should validate required fields", async () => {
  const ctx = createAuthenticatedContext();
  const caller = createTestCaller(ctx);

  const invalidData = {
    name: "", // Should fail validation
  };

  await expect(caller.router.create(invalidData)).rejects.toThrow();
});
```

#### Testing Business Logic

```typescript
it("should enforce business rules", async () => {
  const ctx = createAuthenticatedContext();
  const caller = createTestCaller(ctx);

  // Setup test data that violates business rules
  const invalidBusinessData = {
    // Data that should fail business logic checks
  };

  await expect(caller.router.create(invalidBusinessData)).rejects.toThrow(
    expect.objectContaining({
      code: "BAD_REQUEST",
      message: "Specific business rule violation message",
    })
  );
});
```

## Best Practices

### 1. Test Organization

- Group related tests using `describe` blocks
- Use descriptive test names that explain the scenario
- Test both success and failure cases
- Include edge cases and boundary conditions

### 2. Mocking

- Mock external dependencies (email services, APIs, etc.)
- Use database mocks for consistent, fast tests
- Reset mocks between tests to avoid interference

### 3. Assertions

- Use specific assertions that verify exact behavior
- Test return values, side effects, and error conditions
- Verify that mocks are called with expected parameters

### 4. Test Data

- Use test fixtures for consistent test data
- Generate unique IDs for each test to avoid conflicts
- Create realistic test scenarios that match production use cases

## Coverage Goals

Aim for high test coverage across:

- **Procedures**: All router procedures should be tested
- **Authentication**: All auth scenarios covered
- **Authorization**: All permission checks verified
- **Validation**: All input validation tested
- **Error Handling**: All error paths covered
- **Business Logic**: All business rules verified

## Continuous Integration

Tests run automatically on:
- Pull requests
- Main branch commits
- Scheduled nightly runs

Ensure all tests pass before merging code.

## Troubleshooting

### Common Issues

1. **Mock not working**: Ensure mocks are reset between tests
2. **Async issues**: Use proper async/await in tests
3. **Type errors**: Ensure test data matches expected types
4. **Database errors**: Check that database mocks are properly configured

### Debugging

- Use `console.log` in tests for debugging
- Run single tests with `pnpm test path/to/test.ts`
- Use the test UI for interactive debugging: `pnpm test:ui`
