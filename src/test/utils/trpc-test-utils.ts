import { createId } from "@paralleldrive/cuid2";
import type { inferProcedureInput, inferProcedureOutput } from "@trpc/server";
import { vi } from "vitest";
import type { AppRouter } from "@/lib/trpc";
import { appRouter } from "@/lib/trpc";
import { createContext } from "@/lib/trpc/core/context";

// Mock database
export const mockDb = {
  query: {
    user: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    organization: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    job: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    bid: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    property: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    trade: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    membership: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    address: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    message: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    chat: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    review: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    schedule: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    jobTemplate: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
  },
  insert: vi.fn().mockReturnValue({
    values: vi.fn().mockReturnValue({
      returning: vi.fn(),
    }),
  }),
  update: vi.fn().mockReturnValue({
    set: vi.fn().mockReturnValue({
      where: vi.fn().mockReturnValue({
        returning: vi.fn(),
      }),
    }),
  }),
  delete: vi.fn().mockReturnValue({
    where: vi.fn().mockReturnValue({
      returning: vi.fn(),
    }),
  }),
  $count: vi.fn(),
};

// Mock the environment module
vi.mock("@/env", () => ({
  env: {
    DATABASE_URL: "postgresql://test:test@localhost:5432/test",
    NEXTAUTH_SECRET: "test-secret",
    NEXTAUTH_URL: "http://localhost:3000",
    RESEND_API_KEY: "test-resend-key",
    PUSHER_APP_ID: "test-pusher-app-id",
    PUSHER_SECRET: "test-pusher-secret",
    VAPID_PRIVATE_KEY: "test-vapid-private-key",
    OPENAI_API_KEY: "test-openai-key",
    GEOCODING_API_KEY: "test-geocoding-key",
  },
}));

// Mock the database module
vi.mock("@/db", async () => {
  const actual = await vi.importActual("@/db");
  return {
    ...actual,
    db: mockDb,
  };
});

/**
 * Create a test context with optional user authentication
 */
export function createTestContext(
  options: {
    userId?: string;
    userRole?: "homeowner" | "contractor" | "admin";
    organizationId?: string;
  } = {},
) {
  return createContext({
    req: {} as any,
    resHeaders: {} as any,
  });
}

/**
 * Create a test caller for tRPC procedures
 */
export function createTestCaller(
  context: Awaited<ReturnType<typeof createTestContext>>,
) {
  return appRouter.createCaller(context);
}

/**
 * Helper to create authenticated test context
 */
export function createAuthenticatedContext(
  userId: string = createId(),
  userRole: "homeowner" | "contractor" | "admin" = "homeowner",
  organizationId?: string,
) {
  const context = createTestContext({ userId, userRole, organizationId });
  // Mock the context to include user info
  (context as any).userId = userId;
  (context as any).userRole = userRole;
  (context as any).organizationId = organizationId;
  return context;
}

/**
 * Helper to create unauthenticated test context
 */
export function createUnauthenticatedContext() {
  return createTestContext();
}

/**
 * Type helpers for procedure inputs and outputs
 */
export type RouterInputs = inferProcedureInput<AppRouter>;
export type RouterOutputs = inferProcedureOutput<AppRouter>;

/**
 * Reset all database mocks
 */
export function resetDbMocks() {
  vi.clearAllMocks();
  Object.values(mockDb.query).forEach((table) => {
    Object.values(table).forEach((method) => {
      if (vi.isMockFunction(method)) {
        method.mockReset();
      }
    });
  });

  if (vi.isMockFunction(mockDb.insert)) mockDb.insert.mockReset();
  if (vi.isMockFunction(mockDb.update)) mockDb.update.mockReset();
  if (vi.isMockFunction(mockDb.delete)) mockDb.delete.mockReset();
  if (vi.isMockFunction(mockDb.$count)) mockDb.$count.mockReset();
}

/**
 * Mock successful database operations
 */
export function mockDbSuccess() {
  mockDb.insert.mockReturnValue({
    values: vi.fn().mockReturnValue({
      returning: vi.fn().mockResolvedValue([{ id: createId() }]),
    }),
  });

  mockDb.update.mockReturnValue({
    set: vi.fn().mockReturnValue({
      where: vi.fn().mockReturnValue({
        returning: vi.fn().mockResolvedValue([{ id: createId() }]),
      }),
    }),
  });

  mockDb.delete.mockReturnValue({
    where: vi.fn().mockReturnValue({
      returning: vi.fn().mockResolvedValue([{ id: createId() }]),
    }),
  });
}

/**
 * Mock database error
 */
export function mockDbError(error: Error = new Error("Database error")) {
  Object.values(mockDb.query).forEach((table) => {
    Object.values(table).forEach((method) => {
      if (vi.isMockFunction(method)) {
        method.mockRejectedValue(error);
      }
    });
  });
}
