import { createId } from "@paralleldrive/cuid2";
import { vi } from "vitest";
import { appRouter } from "@/lib/trpc";

// Mock the environment module
vi.mock("@/env", () => ({
  env: {
    DATABASE_URL: "postgresql://test:test@localhost:5432/test",
    NEXTAUTH_SECRET: "test-secret",
    NEXTAUTH_URL: "http://localhost:3000",
    RESEND_API_KEY: "test-resend-key",
    PUSHER_APP_ID: "test-pusher-app-id",
    PUSHER_SECRET: "test-pusher-secret",
    VAPID_PRIVATE_KEY: "test-vapid-private-key",
    OPENAI_API_KEY: "test-openai-key",
    GEOCODING_API_KEY: "test-geocoding-key",
  },
}));

// Mock the auth module
vi.mock("@/lib/auth", () => ({
  auth: {
    api: {
      getSession: vi.fn(),
    },
  },
}));

// Mock Next.js headers
vi.mock("next/headers", () => ({
  headers: vi.fn().mockResolvedValue(new Headers()),
}));

// Mock the database module with factory function
vi.mock("@/db", () => {
  const mockDb = {
    query: {
      user: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      organization: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      job: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      bid: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      property: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      trade: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      membership: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      address: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      message: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      chat: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      review: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      schedule: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      jobTemplate: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
    },
    insert: vi.fn(() => ({
      values: vi.fn(() => ({
        returning: vi.fn(),
      })),
    })),
    update: vi.fn(() => ({
      set: vi.fn(() => ({
        where: vi.fn(() => ({
          returning: vi.fn(),
        })),
      })),
    })),
    delete: vi.fn(() => ({
      where: vi.fn(() => ({
        returning: vi.fn(),
      })),
    })),
    $count: vi.fn().mockReturnValue({
      as: vi.fn().mockReturnValue("organizationCount"),
    }),
  };

  return { db: mockDb };
});

/**
 * Get the mocked database instance for use in tests
 */
export function getMockDb() {
  const { db } = require("@/db");
  return vi.mocked(db);
}

/**
 * Create a test context with optional user authentication
 */
export function createTestContext(
  options: {
    userId?: string;
    userRole?: "homeowner" | "contractor" | "admin";
    organizationId?: string;
  } = {},
) {
  // Return a context that matches the actual Context type
  return {
    userId: options.userId,
    role: options.userRole,
  };
}

/**
 * Create a test caller for tRPC procedures
 */
export function createTestCaller(
  context: ReturnType<typeof createTestContext>,
) {
  return appRouter.createCaller(context);
}

/**
 * Helper to create authenticated test context
 */
export function createAuthenticatedContext(
  userId: string = createId(),
  userRole: "homeowner" | "contractor" | "admin" = "homeowner",
  organizationId?: string,
) {
  // Mock the auth session
  const { auth } = require("@/lib/auth");
  vi.mocked(auth.api.getSession).mockResolvedValue({
    user: {
      id: userId,
      role: userRole,
    },
  });

  return createTestContext({ userId, userRole, organizationId });
}

/**
 * Helper to create unauthenticated test context
 */
export function createUnauthenticatedContext() {
  // Mock no session
  const { auth } = require("@/lib/auth");
  vi.mocked(auth.api.getSession).mockResolvedValue(null);

  return createTestContext();
}

/**
 * Type helpers for procedure inputs and outputs
 */
export type RouterInputs = any; // Simplified for testing
export type RouterOutputs = any; // Simplified for testing

/**
 * Reset all database mocks
 */
export function resetDbMocks() {
  vi.clearAllMocks();
}

/**
 * Mock successful database operations
 */
export function mockDbSuccess() {
  // The Drizzle mock handles this automatically
  // This function is kept for compatibility
}

/**
 * Mock database error
 */
export function mockDbError(error: Error = new Error("Database error")) {
  // The Drizzle mock handles this automatically
  // This function is kept for compatibility
}
