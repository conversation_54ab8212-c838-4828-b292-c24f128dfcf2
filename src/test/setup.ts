import "@testing-library/jest-dom";
import { afterAll, afterEach, beforeAll } from "vitest";
import { server } from "./mocks/server";

// Establish API mocking before all tests
beforeAll(() => {
  server.listen({ onUnhandledRequest: "error" });
});

// Reset any request handlers that we may add during the tests,
// so they don't affect other tests
afterEach(() => {
  server.resetHandlers();
});

// Clean up after the tests are finished
afterAll(() => {
  server.close();
});

// Mock environment variables for testing
// NODE_ENV is already set to "test" by Vitest
process.env.DATABASE_URL = "postgresql://test:test@localhost:5432/test";
process.env.NEXTAUTH_SECRET = "test-secret";
process.env.NEXTAUTH_URL = "http://localhost:3000";

// Required public environment variables
process.env.NEXT_PUBLIC_POSTHOG_KEY = "test-posthog-key";
process.env.NEXT_PUBLIC_POSTHOG_HOST = "https://test-posthog.com";
process.env.NEXT_PUBLIC_STORAGE_URL = "https://test-storage.com";
process.env.NEXT_PUBLIC_PUSHER_KEY = "test-pusher-key";
process.env.NEXT_PUBLIC_PUSHER_CLUSTER = "test-cluster";
process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY = "test-vapid-key";

// Other required environment variables
process.env.RESEND_API_KEY = "test-resend-key";
process.env.PUSHER_APP_ID = "test-pusher-app-id";
process.env.PUSHER_SECRET = "test-pusher-secret";
process.env.VAPID_PRIVATE_KEY = "test-vapid-private-key";
process.env.OPENAI_API_KEY = "test-openai-key";
process.env.GEOCODING_API_KEY = "test-geocoding-key";
