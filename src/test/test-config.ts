/**
 * Test Configuration
 * 
 * Centralized configuration for test environment
 */

export const TEST_CONFIG = {
  // Database configuration
  database: {
    url: "postgresql://test:test@localhost:5432/test",
    maxConnections: 1,
    timeout: 5000,
  },

  // Authentication configuration
  auth: {
    secret: "test-secret-key",
    sessionDuration: 3600, // 1 hour
  },

  // API configuration
  api: {
    baseUrl: "http://localhost:3000",
    timeout: 10000,
  },

  // Test data configuration
  testData: {
    defaultUserId: "test-user-id",
    defaultOrganizationId: "test-org-id",
    defaultPropertyId: "test-property-id",
  },

  // Mock configuration
  mocks: {
    enableNetworkMocks: true,
    enableDatabaseMocks: true,
    enableEmailMocks: true,
  },

  // Performance thresholds
  performance: {
    maxQueryTime: 1000, // ms
    maxMutationTime: 2000, // ms
  },
} as const;

/**
 * Test environment variables
 */
export const TEST_ENV = {
  NODE_ENV: "test",
  DATABASE_URL: TEST_CONFIG.database.url,
  NEXTAUTH_SECRET: TEST_CONFIG.auth.secret,
  NEXTAUTH_URL: TEST_CONFIG.api.baseUrl,
  // Add other test-specific environment variables
} as const;

/**
 * Apply test environment variables
 */
export function setupTestEnvironment() {
  Object.entries(TEST_ENV).forEach(([key, value]) => {
    process.env[key] = value;
  });
}

/**
 * Test timeouts
 */
export const TEST_TIMEOUTS = {
  unit: 5000,      // 5 seconds for unit tests
  integration: 10000, // 10 seconds for integration tests
  e2e: 30000,      // 30 seconds for e2e tests
} as const;
